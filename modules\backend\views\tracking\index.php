<?php

use app\common\models\Orders;
use yii\helpers\Html;
use yii\widgets\LinkPager;

$this->title = Yii::t("app", "tracking_section");
?>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-3">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>
        <div class="col-md-9">
            <div class="d-flex justify-content-end">
                <div class="form-group mr-2">
                    <select class="form-control" name="seller_id" id="seller-filter">
                        <option value="" selected><?= Yii::t('app', 'select_seller') ?></option>
                        <?php foreach (\app\common\models\User::find()->all() as $seller): ?>
                            <option value="<?= $seller->id ?>" <?= Yii::$app->request->get('seller_id') == $seller->id ? 'selected' : '' ?>>
                                <?= Html::encode($seller->full_name) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>



                <div class="form-group mr-2">
                    <select class="form-control" name="cashbox_id" id="cashbox-filter">
                        <option value=""><?= Yii::t('app', 'all') ?></option>
                        <?php foreach (\app\common\models\Cashbox::find()->all() as $cashbox): ?>
                            <?php if ($cashbox->is_main) continue; ?>
                            <option value="<?= $cashbox->id ?>" <?= Yii::$app->request->get('cashbox_id') == $cashbox->id ? 'selected' : '' ?>>
                                <?= Html::encode($cashbox->name) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group mr-2">
                    <input type="date" class="form-control" name="startDate" id="start-date"
                           value="<?= Yii::$app->request->get('startDate', date('Y-m-d')) ?>">
                </div>

                <div class="form-group mr-2">
                    <input type="date" class="form-control" name="endDate" id="end-date"
                           value="<?= Yii::$app->request->get('endDate', date('Y-m-d')) ?>">
                </div>

                <div class="form-group">
                    <button type="button" class="btn btn-primary" id="apply-filters"><?= Yii::t('app', 'filter') ?></button>
                </div>
            </div>
        </div>
    </div>

    <div class="tracking-index">
        <?php if (!empty($detailedOrders)): ?>
            <?php
            // Все итоговые суммы получены из контроллера в $footerTotals
            // Округляем суммы в соответствии с требованиями
            $totalBenefit = round(floatval($footerTotals['total_benefit'] ?? 0), 2);     // До 2 знаков
            $totalBenefitSom = round(floatval($footerTotals['total_benefit_som'] ?? 0));  // До целого
            $totalSum = round(floatval($footerTotals['total_sum'] ?? 0), 2);              // До 2 знаков - Умумий сумма (116.00) - сумма (size_meter * price)
            $totalPaidSum = round(floatval($footerTotals['total_paid_sum'] ?? 0));         // До целого - Туглов сумма (3,375,000) - сумма paid_sum
            $totalAllPrice = round(floatval($footerTotals['total_all_price'] ?? 0));      // До целого - Выручка нархи (3,855,000) - сумма sell_price
            ?>

            <table id="tracking-table" class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th><?= Yii::t('app', 'ID') ?></th>
                        <th><?= Yii::t('app', 'sell_date') . '<br>' .'/'. ' ' .  Yii::t('app', 'return_date') ?></th>
                        <th><?= Yii::t('app', 'display_reason') ?></th>
                        <th><?= Yii::t('app', 'user_name') ?></th>
                        <th><?= Yii::t('app', 'storage') ?></th>
                        <th><?= Yii::t('app', 'product_name') ?></th>
                        <th><?= Yii::t('app', 'sale_area') ?></th>
                        <th><?= Yii::t('app', 'sell_price') ?></th>
                        <th><?= Yii::t('app', 'all_price') ?></th>
                        <th><?= Yii::t('app', 'benefit_order_$') ?></th>
                        <th><?= Yii::t('app', message: 'benefit_order_som') ?></th>
                        <th><?= Yii::t('app', 'order_price') ?></th>
                        <th><?= Yii::t('app', 'paid_sum') ?></th>
                        <th><?= Yii::t('app', 'cashbox_name') ?></th>
                        <th><?= Yii::t('app', 'pay_status') ?></th>
                        <th><?= Yii::t('app', 'client_name') ?></th>
                        <th><?= Yii::t('app', 'client_phone_number') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($detailedOrders as $order): ?>
                        <?php
                        // Декодируем продукты, если они есть
                        $products = !empty($order['products']) && is_string($order['products'])
                            ? json_decode($order['products'], true)
                            : [];
                        $rowspan = count($products) > 0 ? count($products) : 1;
                        ?>

                        <?php if (empty($products)): ?>
                            <tr>
                                <td><?= Html::encode($order['id']) ?></td>
                                <td>
                                <?php
                                 if ($order['type'] == Orders::TYPE_RETURN || $order['type'] == Orders::TYPE_RETURN_ALL) {
                                     // Для возвратов показываем дату возврата
                                     $returnDate = $order['return_created_at'] ? $order['return_created_at'] : $order['accepted_date'];
                                     echo Html::encode(date('d.m.Y H:i', strtotime($returnDate)));
                                 } else {
                                     // Для обычных заказов показываем дату создания и дату подтверждения
                                     echo Html::encode(date('d.m.Y H:i', strtotime($order['created_at']))) . '<br>';
                                     echo Html::encode(date('d.m.Y H:i', strtotime($order['accepted_date'])));
                                 }
                                ?>
                            </td>
                                <td>
                                    <?php if (isset($order['record_type']) && $order['record_type'] == 'return'): ?>
                                        <span class="badge badge-danger"><?= Yii::t('app', 'returned') ?></span>
                                    <?php else: ?>
                                        <span class="badge badge-info"><?= Html::encode($order['display_reason']) ?></span>
                                    <?php endif; ?>
                                </td>
                                <td><?= Html::encode($order['seller_name']) ?></td>
                                <td><?= Html::encode($order['storage_name'] ?? 'N/A') ?></td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                                <?php if (isset($order['record_type']) && $order['record_type'] == 'return'): ?>
                                    <!-- Для записей возврата показываем суммы возврата с красным цветом -->
                                    <td><span style="color: red;"><?= Yii::$app->formatter->asDecimal($order['benefit'], 2) ?></span></td>
                                    <td><span style="color: red;"><?php
                                        // Отладка: показываем исходное значение
                                        $original = $order['benefit_som'];
                                        // Очищаем от всех символов кроме цифр, точки и минуса
                                        $cleanValue = preg_replace('/[^0-9.-]/', '', $order['benefit_som']);
                                        $benefitSom = floatval($cleanValue);
                                        $remainder = abs($benefitSom) % 1000;
                                        $roundedBenefitSom = ($benefitSom - ($benefitSom >= 0 ? $remainder : -$remainder)) + (($remainder > 500) ? ($benefitSom >= 0 ? 1000 : -1000) : 0);
                                        echo number_format((int)$roundedBenefitSom, 0, '.', ',');
                                        // Временная отладка
                                        echo "<br><small>Orig: $original | Clean: $cleanValue | Float: $benefitSom | Rounded: $roundedBenefitSom</small>";
                                    ?></span></td>
                                    <td><span style="color: red;"><?= Yii::$app->formatter->asDecimal($order['total_sell_price'], 0) ?></span></td>
                                    <td><span style="color: red;"><?= Yii::$app->formatter->asDecimal($order['paid_sum'], 0) ?></span></td>
                                <?php else: ?>
                                    <!-- Для обычных заказов -->
                                    <td><?= Yii::$app->formatter->asDecimal($order['benefit'], 2) ?></td>
                                    <td><?php
                                        // Очищаем от всех символов кроме цифр, точки и минуса
                                        $cleanValue = preg_replace('/[^0-9.-]/', '', $order['benefit_som']);
                                        $benefitSom = floatval($cleanValue);
                                        $remainder = abs($benefitSom) % 1000;
                                        $roundedBenefitSom = ($benefitSom - ($benefitSom >= 0 ? $remainder : -$remainder)) + (($remainder > 500) ? ($benefitSom >= 0 ? 1000 : -1000) : 0);
                                        echo number_format((int)$roundedBenefitSom, 0, '.', ',');
                                    ?></td>
                                    <td><?= Yii::$app->formatter->asDecimal($order['total_sell_price'], 0) ?></td>
                                    <td><?= Yii::$app->formatter->asDecimal($order['paid_sum'], 0) ?></td>
                                <?php endif; ?>
                                <td><?= Html::encode($order['cashbox_name']) ?></td>
                                <td><?= $order['pay_status'] == Orders::PAY_STATUS_PAID ? Yii::t('app', 'paid') : Yii::t('app', 'not_paid') ?></td>
                                <td><?= Html::encode($order['client_name']) ?></td>
                                <td><?= Html::encode($order['phone_number']) ?></td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($products as $index => $product): ?>
                                <tr>
                                    <?php if ($index === 0): ?>
                                        <td rowspan="<?= $rowspan ?>"><?= Html::encode($order['id']) ?></td>
                                        <td rowspan="<?= $rowspan ?>">
                                            <?php
                                            if ($order['type'] == Orders::TYPE_RETURN || $order['type'] == Orders::TYPE_RETURN_ALL) {
                                                // Для возвратов показываем дату возврата
                                                $returnDate = $order['return_created_at'] ? $order['return_created_at'] : $order['accepted_date'];
                                                echo Html::encode(date('d.m.Y H:i', strtotime($returnDate)));
                                            } else {
                                                // Для обычных заказов показываем дату создания и дату подтверждения
                                                echo Html::encode(date('d.m.Y H:i', strtotime($order['created_at']))) . '<br>';
                                                echo Html::encode(date('d.m.Y H:i', strtotime($order['accepted_date'])));
                                            }
                                            ?>
                                        </td>
                                        <td rowspan="<?= $rowspan ?>">
                                            <?php if (isset($order['record_type']) && $order['record_type'] == 'return'): ?>
                                                <span class="badge badge-danger"><?= Yii::t('app', 'returned') ?></span>
                                            <?php else: ?>
                                                <span class="badge badge-info"><?= Html::encode($order['display_reason']) ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td rowspan="<?= $rowspan ?>"><?= Html::encode($order['seller_name']) ?></td>
                                        <td rowspan="<?= $rowspan ?>"><?= Html::encode($order['storage_name'] ?? 'N/A') ?></td>
                                    <?php endif; ?>

                                    <?php if ($product['has_return']): ?>
                                        <td><span style="text-decoration: line-through; color: red;"><?= Html::encode($product['name']) ?></span></td>
                                    <?php else: ?>
                                        <td><?= Html::encode($product['name']) ?></td>
                                    <?php endif; ?>
                                    <td><?= Html::encode($product['size_meter']) ?></td>
                                    <td><?= Html::encode($product['price']) ?></td>
                                    <?php if (isset($order['record_type']) && $order['record_type'] == 'return'): ?>
                                        <td><span style="color: red;"><?= Html::encode($product['sell_price']) ?></span></td>
                                    <?php else: ?>
                                        <td><?= Html::encode($product['sell_price']) ?></td>
                                    <?php endif; ?>

                                    <?php if ($index === 0): ?>
                                        <?php if (isset($order['record_type']) && $order['record_type'] == 'return'): ?>
                                            <!-- Для записей возврата показываем суммы возврата с красным цветом -->
                                            <td rowspan="<?= $rowspan ?>"><span style="color: red;"><?= Yii::$app->formatter->asDecimal($order['benefit'], 2) ?></span></td>
                                            <td rowspan="<?= $rowspan ?>"><span style="color: red;"><?= Yii::$app->formatter->asDecimal($order['benefit_som'], 0) ?></span></td>
                                            <td rowspan="<?= $rowspan ?>"><span style="color: red;"><?= Yii::$app->formatter->asDecimal($order['total_sell_price'], 0) ?></span></td>
                                            <td rowspan="<?= $rowspan ?>"><span style="color: red;"><?= Yii::$app->formatter->asDecimal($order['paid_sum'], 0) ?></span></td>
                                        <?php else: ?>
                                            <!-- Для обычных заказов -->
                                            <td rowspan="<?= $rowspan ?>"><?= Yii::$app->formatter->asDecimal($order['benefit'], 2) ?></td>
                                            <td rowspan="<?= $rowspan ?>"><?= Yii::$app->formatter->asDecimal($order['benefit_som'], 0) ?></td>
                                            <td rowspan="<?= $rowspan ?>"><?= Yii::$app->formatter->asDecimal($order['total_sell_price'], 0) ?></td>
                                            <td rowspan="<?= $rowspan ?>"><?= Yii::$app->formatter->asDecimal($order['paid_sum'], 0) ?></td>
                                        <?php endif; ?>
                                        <td rowspan="<?= $rowspan ?>"><?= Html::encode($order['cashbox_name']) ?></td>
                                        <td rowspan="<?= $rowspan ?>">
                                            <?= $order['pay_status'] == Orders::PAY_STATUS_PAID ? Yii::t('app', 'paid') : Yii::t('app', 'not_paid') ?>
                                        </td>
                                        <td rowspan="<?= $rowspan ?>"><?= Html::encode($order['client_name']) ?></td>
                                        <td rowspan="<?= $rowspan ?>"><?= Html::encode($order['phone_number']) ?></td>
                                    <?php endif; ?>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </tbody>
                <tfoot>
                    <tr>
                        <th colspan="8" class="text-right"><?= Yii::t('app', 'all') ?>:</th>
                        <th><?= Yii::$app->formatter->asDecimal($totalSum, 2) ?></th>
                        <th><?= Yii::$app->formatter->asDecimal($totalBenefit, 2) ?></th>
                        <th><?= Yii::$app->formatter->asDecimal($totalBenefitSom, 0) ?></th>
                        <th><?= Yii::$app->formatter->asDecimal($totalAllPrice, 0) ?></th>
                        <th><?= Yii::$app->formatter->asDecimal($totalPaidSum, 0) ?></th>
                        <th colspan="4"></th>
                    </tr>
                </tfoot>
            </table>
        <?php else: ?>
            <div class="text-center mt-6 red-text">
                <?= Yii::t('app', 'No data available.') ?>
            </div>
    <?php endif; ?>
</div>



<div class="pagination-wrapper text-center mt-3">
    <?php if (!empty($detailedOrders) && $totalCount > $pageSize): ?>
        <?= LinkPager::widget([
            'pagination' => new \yii\data\Pagination([
                'totalCount' => $totalCount ?? 0,
                'pageSize' => $pageSize,
                'page' => $page - 1,
            ]),
            'options' => ['class' => 'pagination justify-content-center'],
            'linkOptions' => ['class' => 'page-link'],
            'linkContainerOptions' => ['class' => 'page-item'],
            'disabledListItemSubTagOptions' => ['tag' => 'a', 'class' => 'page-link'],
        ]) ?>
    <?php endif; ?>
</div>
</div>

<?php
// Подключаем Select2 CSS
$this->registerCssFile('https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css');

// Подключаем Select2 JS
$this->registerJsFile('https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js', ['depends' => [\yii\web\JqueryAsset::class]]);

// Добавляем стили для Select2
$css = <<<CSS
#seller-filter, .select2-container--default .select2-selection--single {
    width: 180px !important;
    min-width: 180px !important;
    max-width: 100%;
}
.select2-container {
    width: 180px !important;
    min-width: 180px !important;
    max-width: 100%;
}
CSS;
$this->registerCss($css);

$js = <<<JS
$(document).ready(function() {
    // Инициализируем Select2
    $('#seller-filter').select2({
        placeholder: "Выберите продавца",
        allowClear: true,
        width: 'style'
    });

    $('#apply-filters').on('click', function() {
        loadData();
    });

    function loadData(page = 1) {
        var data = {
            seller_id: $('#seller-filter').val(),
            cashbox_id: $('#cashbox-filter').val(),
            startDate: $('#start-date').val(),
            endDate: $('#end-date').val(),
            page: page
        };

        $.ajax({
            url: window.location.pathname,
            data: data,
            type: 'GET',
            success: function(response) {
                $('.tracking-index').html($(response).find('.tracking-index').html());
                $('.pagination-wrapper').html($(response).find('.pagination-wrapper').html());

                // Переинициализируем Select2 после AJAX загрузки
                $('#seller-filter').select2({
                    placeholder: "Выберите продавца",
                    allowClear: true,
                    width: 'style'
                });

                var params = new URLSearchParams(data);
                var newUrl = window.location.pathname + '?' + params.toString();
                window.history.pushState({}, '', newUrl);
            }
        });
    }

    $(document).on('click', '.pagination a', function(e) {
        e.preventDefault();
        var pageZeroBased = $(this).data('page');   
        if (pageZeroBased !== undefined) {
             var pageOneBased = parseInt(pageZeroBased) + 1;
             loadData(pageOneBased);
        }
    });
});
JS;
$this->registerJs($js);
?>